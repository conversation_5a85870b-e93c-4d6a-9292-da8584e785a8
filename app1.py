from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

import streamlit as st
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configuration for different LLM providers
USE_OPENAI = False  # Set to True to use OpenAI, False to use mock responses

if USE_OPENAI:
    os.environ["OPENAI_API_KEY"]=os.getenv("OPENAI_API_KEY")
    ## Langmith tracking
    os.environ["LANGCHAIN_TRACING_V2"]="true"
    os.environ["LANGCHAIN_API_KEY"]=os.getenv("LANGCHAIN_API_KEY")

## Prompt Template

prompt=ChatPromptTemplate.from_messages(
    [
        ("system","You are a helpful assistant. Please response to the user queries"),
        ("user","Question:{question}")
    ]
)

## streamlit framework

if USE_OPENAI:
    st.title('🤖 Langchain Demo With OpenAI API')
else:
    st.title('🤖 Langchain Demo (Mock Mode)')
    st.warning("⚠️ **Demo Mode Active**: OpenAI API is disabled. Using mock responses for demonstration.")
input_text=st.text_input("Search the topic u want")

# LLM Configuration
if USE_OPENAI:
    # OpenAI LLM (requires valid API key and quota)
    llm = ChatOpenAI(model="gpt-3.5-turbo")
    output_parser = StrOutputParser()
    chain = prompt | llm | output_parser

    if input_text:
        try:
            st.write(chain.invoke({'question': input_text}))
        except Exception as e:
            st.error(f"OpenAI API Error: {str(e)}")
            st.info("💡 **Tip:** Check your OpenAI billing and quota at https://platform.openai.com/")
else:
    # Mock LLM for demonstration (no API key required)
    if input_text:
        st.info("🤖 **Demo Mode**: Using mock responses (OpenAI API disabled)")

        # Simple mock responses based on keywords
        mock_responses = {
            "hello": "Hello! How can I help you today?",
            "weather": "I'm a demo chatbot and can't check real weather, but I hope it's nice where you are!",
            "python": "Python is a great programming language! It's versatile and beginner-friendly.",
            "ai": "Artificial Intelligence is fascinating! It's transforming many industries.",
            "langchain": "LangChain is a framework for developing applications powered by language models!",
            "streamlit": "Streamlit is an amazing tool for building data apps quickly!",
        }

        # Find matching response or provide default
        response = "I'm a helpful assistant in demo mode. I can respond to questions about: hello, weather, python, ai, langchain, streamlit, or anything else!"

        for keyword, mock_response in mock_responses.items():
            if keyword.lower() in input_text.lower():
                response = mock_response
                break

        st.write(f"**Response:** {response}")
        st.caption("💡 To use real AI responses, set USE_OPENAI=True and ensure you have OpenAI credits.")